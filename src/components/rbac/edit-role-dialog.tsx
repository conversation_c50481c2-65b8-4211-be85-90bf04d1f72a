"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { UserCog, Key } from "lucide-react";
import { toast } from "sonner";

const editRoleSchema = z.object({
  name: z.string().min(1, "Role name is required").max(50, "Role name too long").optional(),
  displayName: z.string().min(1, "Display name is required").max(100, "Display name too long").optional(),
  description: z.string().optional(),
  permissions: z.array(z.string()).optional(),
});

type EditRoleFormValues = z.infer<typeof editRoleSchema>;

interface Permission {
  name: string;
  displayName: string;
  description?: string;
  category: string;
}

interface PermissionsByCategory {
  [category: string]: Permission[];
}

interface Role {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  isSystemRole: boolean;
  permissions: Permission[];
  userCount: number;
}

interface EditRoleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  role: Role;
  onSuccess: () => void;
}

export function EditRoleDialog({ open, onOpenChange, role, onSuccess }: EditRoleDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [allPermissions, setAllPermissions] = useState<PermissionsByCategory>({});
  const [loadingPermissions, setLoadingPermissions] = useState(false);

  const form = useForm<EditRoleFormValues>({
    resolver: zodResolver(editRoleSchema),
    defaultValues: {
      name: role.name,
      displayName: role.displayName,
      description: role.description || "",
      permissions: role.permissions.map(p => p.name),
    },
  });

  const fetchPermissions = async () => {
    try {
      setLoadingPermissions(true);
      const response = await fetch("/api/rbac/permissions");
      if (!response.ok) {
        throw new Error("Failed to fetch permissions");
      }
      const data = await response.json();
      setAllPermissions(data.data.permissions);
    } catch (error) {
      console.error("Error fetching permissions:", error);
      toast.error("Failed to fetch permissions");
    } finally {
      setLoadingPermissions(false);
    }
  };

  useEffect(() => {
    if (open) {
      fetchPermissions();
      // Reset form with current role data
      form.reset({
        name: role.name,
        displayName: role.displayName,
        description: role.description || "",
        permissions: role.permissions.map(p => p.name),
      });
    }
  }, [open, role, form]);

  const onSubmit = async (data: EditRoleFormValues) => {
    try {
      setIsSubmitting(true);
      
      const response = await fetch(`/api/rbac/roles/${role.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update role");
      }

      toast.success("Role updated successfully");
      onSuccess();
    } catch (error) {
      console.error("Error updating role:", error);
      toast.error(error instanceof Error ? error.message : "Failed to update role");
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedPermissions = form.watch("permissions") || [];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserCog className="h-5 w-5" />
            Edit Role: {role.displayName}
          </DialogTitle>
          <DialogDescription>
            Update role information and permissions
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Role Information</CardTitle>
                <CardDescription>Update basic role information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Role Name</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="e.g., CARBON_ANALYST" 
                            {...field}
                            onChange={(e) => {
                              const value = e.target.value.toUpperCase().replace(/[^A-Z0-9_]/g, '_');
                              field.onChange(value);
                            }}
                          />
                        </FormControl>
                        <FormDescription>
                          Internal name for the role (uppercase, underscores only)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="displayName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Display Name</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., Carbon Analyst" {...field} />
                        </FormControl>
                        <FormDescription>
                          Human-readable name shown in the interface
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Describe what this role is for and what responsibilities it includes..."
                          className="min-h-[100px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        Optional description to help users understand the role's purpose
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Permissions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Key className="h-5 w-5" />
                  Permissions
                </CardTitle>
                <CardDescription>
                  Update the permissions for this role
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loadingPermissions ? (
                  <div className="text-center py-8">Loading permissions...</div>
                ) : (
                  <FormField
                    control={form.control}
                    name="permissions"
                    render={() => (
                      <FormItem>
                        <div className="space-y-6">
                          {Object.entries(allPermissions).map(([category, categoryPermissions]) => (
                            <div key={category} className="space-y-3">
                              <div className="flex items-center justify-between">
                                <h4 className="font-medium text-sm">{category}</h4>
                                <Badge variant="outline" className="text-xs">
                                  {categoryPermissions.filter(p => selectedPermissions.includes(p.name)).length} / {categoryPermissions.length}
                                </Badge>
                              </div>
                              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                {categoryPermissions.map((permission) => (
                                  <FormField
                                    key={permission.name}
                                    control={form.control}
                                    name="permissions"
                                    render={({ field }) => {
                                      return (
                                        <FormItem
                                          key={permission.name}
                                          className="flex flex-row items-center space-x-3 space-y-0 p-3 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors"
                                          onClick={() => {
                                            const isChecked = field.value?.includes(permission.name);
                                            if (isChecked) {
                                              field.onChange(
                                                field.value?.filter(
                                                  (value) => value !== permission.name
                                                )
                                              );
                                            } else {
                                              field.onChange([...(field.value || []), permission.name]);
                                            }
                                          }}
                                        >
                                          <FormControl>
                                            <Checkbox
                                              checked={field.value?.includes(permission.name)}
                                              onCheckedChange={(checked) => {
                                                return checked
                                                  ? field.onChange([...(field.value || []), permission.name])
                                                  : field.onChange(
                                                      field.value?.filter(
                                                        (value) => value !== permission.name
                                                      )
                                                    );
                                              }}
                                            />
                                          </FormControl>
                                          <div className="flex-1 space-y-1 leading-none">
                                            <FormLabel className="text-sm font-medium cursor-pointer">
                                              {permission.displayName}
                                            </FormLabel>
                                            {permission.description && (
                                              <FormDescription className="text-xs text-muted-foreground">
                                                {permission.description}
                                              </FormDescription>
                                            )}
                                          </div>
                                        </FormItem>
                                      );
                                    }}
                                  />
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </CardContent>
            </Card>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Updating..." : "Update Role"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
