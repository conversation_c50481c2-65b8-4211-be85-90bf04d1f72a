"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { User<PERSON><PERSON>, <PERSON>, Key, Info } from "lucide-react";
import { toast } from "sonner";

const createRoleSchema = z.object({
  name: z.string().min(1, "Role name is required").max(50, "Role name too long"),
  displayName: z.string().min(1, "Display name is required").max(100, "Display name too long"),
  description: z.string().optional(),
  permissions: z.array(z.string()).default([]),
});

type CreateRoleFormValues = z.infer<typeof createRoleSchema>;

interface Permission {
  name: string;
  displayName: string;
  description?: string;
  category: string;
}

interface PermissionsByCategory {
  [category: string]: Permission[];
}

interface CreateRoleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function CreateRoleDialog({ open, onOpenChange, onSuccess }: CreateRoleDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [permissions, setPermissions] = useState<PermissionsByCategory>({});
  const [loadingPermissions, setLoadingPermissions] = useState(false);

  const form = useForm<CreateRoleFormValues>({
    resolver: zodResolver(createRoleSchema),
    defaultValues: {
      name: "",
      displayName: "",
      description: "",
      permissions: [],
    },
  });

  const fetchPermissions = async () => {
    try {
      setLoadingPermissions(true);
      const response = await fetch("/api/rbac/permissions");
      if (!response.ok) {
        throw new Error("Failed to fetch permissions");
      }
      const data = await response.json();
      setPermissions(data.data.permissions);
    } catch (error) {
      console.error("Error fetching permissions:", error);
      toast.error("Failed to fetch permissions");
    } finally {
      setLoadingPermissions(false);
    }
  };

  useEffect(() => {
    if (open) {
      fetchPermissions();
    }
  }, [open]);

  const onSubmit = async (data: CreateRoleFormValues) => {
    try {
      setIsSubmitting(true);
      
      const response = await fetch("/api/rbac/roles", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create role");
      }

      toast.success("Role created successfully");
      form.reset();
      onSuccess();
    } catch (error) {
      console.error("Error creating role:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create role");
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedPermissions = form.watch("permissions");
  const selectedPermissionsByCategory = Object.entries(permissions).reduce((acc, [category, perms]) => {
    const selectedInCategory = perms.filter(p => selectedPermissions.includes(p.name));
    if (selectedInCategory.length > 0) {
      acc[category] = selectedInCategory;
    }
    return acc;
  }, {} as PermissionsByCategory);

  const totalSelectedPermissions = selectedPermissions.length;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserCog className="h-5 w-5" />
            Create New Role
          </DialogTitle>
          <DialogDescription>
            Create a custom role with specific permissions for your organization
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="basic">Basic Information</TabsTrigger>
                <TabsTrigger value="permissions">Permissions</TabsTrigger>
                <TabsTrigger value="review">Review</TabsTrigger>
              </TabsList>

              {/* Basic Information Tab */}
              <TabsContent value="basic" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Info className="h-5 w-5" />
                      Role Information
                    </CardTitle>
                    <CardDescription>
                      Provide basic information about the role
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Role Name *</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="e.g., CARBON_ANALYST" 
                                {...field}
                                onChange={(e) => {
                                  const value = e.target.value.toUpperCase().replace(/[^A-Z0-9_]/g, '_');
                                  field.onChange(value);
                                }}
                              />
                            </FormControl>
                            <FormDescription>
                              Internal name for the role (uppercase, underscores only)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="displayName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Display Name *</FormLabel>
                            <FormControl>
                              <Input placeholder="e.g., Carbon Analyst" {...field} />
                            </FormControl>
                            <FormDescription>
                              Human-readable name shown in the interface
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="Describe what this role is for and what responsibilities it includes..."
                              className="min-h-[100px]"
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            Optional description to help users understand the role's purpose
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>

                {/* Reference Section */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="h-5 w-5" />
                      Role Reference
                    </CardTitle>
                    <CardDescription>
                      Available system roles and predefined templates for reference
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-sm font-medium mb-2">System Roles</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          <div className="p-3 border rounded-lg">
                            <div className="flex items-center gap-2 mb-1">
                              <Shield className="h-3 w-3 text-blue-500" />
                              <span className="text-sm font-medium">Organization Admin</span>
                            </div>
                            <p className="text-xs text-muted-foreground">Full access to organization features</p>
                          </div>
                          <div className="p-3 border rounded-lg">
                            <div className="flex items-center gap-2 mb-1">
                              <Shield className="h-3 w-3 text-blue-500" />
                              <span className="text-sm font-medium">Department Admin</span>
                            </div>
                            <p className="text-xs text-muted-foreground">Administrative access to a department</p>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium mb-2">Predefined Templates</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          <div className="p-3 border rounded-lg">
                            <div className="flex items-center gap-2 mb-1">
                              <Key className="h-3 w-3 text-green-500" />
                              <span className="text-sm font-medium">Carbon Credit Manager</span>
                            </div>
                            <p className="text-xs text-muted-foreground">Manages carbon credits for the organization</p>
                          </div>
                          <div className="p-3 border rounded-lg">
                            <div className="flex items-center gap-2 mb-1">
                              <Key className="h-3 w-3 text-green-500" />
                              <span className="text-sm font-medium">Wallet Manager</span>
                            </div>
                            <p className="text-xs text-muted-foreground">Manages wallets for the organization</p>
                          </div>
                          <div className="p-3 border rounded-lg">
                            <div className="flex items-center gap-2 mb-1">
                              <Key className="h-3 w-3 text-green-500" />
                              <span className="text-sm font-medium">Finance Manager</span>
                            </div>
                            <p className="text-xs text-muted-foreground">Manages financial aspects</p>
                          </div>
                          <div className="p-3 border rounded-lg">
                            <div className="flex items-center gap-2 mb-1">
                              <Key className="h-3 w-3 text-green-500" />
                              <span className="text-sm font-medium">Read-Only User</span>
                            </div>
                            <p className="text-xs text-muted-foreground">Read-only access to organization features</p>
                          </div>
                          <div className="p-3 border rounded-lg">
                            <div className="flex items-center gap-2 mb-1">
                              <Key className="h-3 w-3 text-orange-500" />
                              <span className="text-sm font-medium">SPV User</span>
                            </div>
                            <p className="text-xs text-muted-foreground">Base role for SPV portal access</p>
                          </div>
                          <div className="p-3 border rounded-lg">
                            <div className="flex items-center gap-2 mb-1">
                              <Key className="h-3 w-3 text-orange-500" />
                              <span className="text-sm font-medium">Site Worker</span>
                            </div>
                            <p className="text-xs text-muted-foreground">Field workers who enter raw data</p>
                          </div>
                          <div className="p-3 border rounded-lg">
                            <div className="flex items-center gap-2 mb-1">
                              <Key className="h-3 w-3 text-orange-500" />
                              <span className="text-sm font-medium">Project Manager</span>
                            </div>
                            <p className="text-xs text-muted-foreground">Manages project data and verification</p>
                          </div>
                          <div className="p-3 border rounded-lg">
                            <div className="flex items-center gap-2 mb-1">
                              <Key className="h-3 w-3 text-orange-500" />
                              <span className="text-sm font-medium">SPV Administrator</span>
                            </div>
                            <p className="text-xs text-muted-foreground">Full SPV management and oversight</p>
                          </div>
                        </div>
                        <p className="text-xs text-muted-foreground mt-2">
                          💡 Tip: You can use the "Create from Template" option in the roles list to quickly create roles based on these templates.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Permissions Tab */}
              <TabsContent value="permissions" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Key className="h-5 w-5" />
                      Permission Assignment
                    </CardTitle>
                    <CardDescription>
                      Select the permissions this role should have
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {loadingPermissions ? (
                      <div className="text-center py-8">Loading permissions...</div>
                    ) : (
                      <FormField
                        control={form.control}
                        name="permissions"
                        render={() => (
                          <FormItem>
                            <div className="space-y-6">
                              {Object.entries(permissions).map(([category, categoryPermissions]) => (
                                <div key={category} className="space-y-3">
                                  <div className="flex items-center justify-between">
                                    <h4 className="font-medium text-sm">{category}</h4>
                                    <Badge variant="outline" className="text-xs">
                                      {categoryPermissions.filter(p => selectedPermissions.includes(p.name)).length} / {categoryPermissions.length}
                                    </Badge>
                                  </div>
                                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                    {categoryPermissions.map((permission) => (
                                      <FormField
                                        key={permission.name}
                                        control={form.control}
                                        name="permissions"
                                        render={({ field }) => {
                                          return (
                                            <FormItem
                                              key={permission.name}
                                              className="flex flex-row items-center space-x-3 space-y-0 p-3 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors"
                                              onClick={() => {
                                                const isChecked = field.value?.includes(permission.name);
                                                if (isChecked) {
                                                  field.onChange(
                                                    field.value?.filter(
                                                      (value) => value !== permission.name
                                                    )
                                                  );
                                                } else {
                                                  field.onChange([...field.value, permission.name]);
                                                }
                                              }}
                                            >
                                              <FormControl>
                                                <Checkbox
                                                  checked={field.value?.includes(permission.name)}
                                                  onCheckedChange={(checked) => {
                                                    return checked
                                                      ? field.onChange([...field.value, permission.name])
                                                      : field.onChange(
                                                          field.value?.filter(
                                                            (value) => value !== permission.name
                                                          )
                                                        );
                                                  }}
                                                />
                                              </FormControl>
                                              <div className="flex-1 space-y-1 leading-none">
                                                <FormLabel className="text-sm font-medium cursor-pointer">
                                                  {permission.displayName}
                                                </FormLabel>
                                                {permission.description && (
                                                  <FormDescription className="text-xs text-muted-foreground">
                                                    {permission.description}
                                                  </FormDescription>
                                                )}
                                              </div>
                                            </FormItem>
                                          );
                                        }}
                                      />
                                    ))}
                                  </div>
                                </div>
                              ))}
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Review Tab */}
              <TabsContent value="review" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="h-5 w-5" />
                      Review Role
                    </CardTitle>
                    <CardDescription>
                      Review the role configuration before creating
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium">Role Name</label>
                        <p className="text-sm text-muted-foreground">{form.watch("name") || "Not set"}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium">Display Name</label>
                        <p className="text-sm text-muted-foreground">{form.watch("displayName") || "Not set"}</p>
                      </div>
                    </div>

                    {form.watch("description") && (
                      <div>
                        <label className="text-sm font-medium">Description</label>
                        <p className="text-sm text-muted-foreground">{form.watch("description")}</p>
                      </div>
                    )}

                    <div>
                      <label className="text-sm font-medium">Permissions ({totalSelectedPermissions})</label>
                      {totalSelectedPermissions === 0 ? (
                        <p className="text-sm text-muted-foreground">No permissions selected</p>
                      ) : (
                        <div className="space-y-3 mt-2">
                          {Object.entries(selectedPermissionsByCategory).map(([category, perms]) => (
                            <div key={category}>
                              <h5 className="text-sm font-medium">{category}</h5>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {perms.map((permission) => (
                                  <Badge key={permission.name} variant="outline" className="text-xs">
                                    {permission.displayName}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Creating..." : "Create Role"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
