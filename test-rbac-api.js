#!/usr/bin/env node

/**
 * Test script to authenticate and test the RBAC API endpoints
 */

// Using built-in fetch (Node.js 18+)

const BASE_URL = 'http://localhost:3000';

// Test credentials
const TEST_USERS = [
  {
    email: '<EMAIL>',
    password: 'password123',
    role: 'ORGANIZATION_ADMIN'
  },
  {
    email: '<EMAIL>',
    password: 'admin123',
    role: 'ADMIN'
  }
];

async function authenticateUser(email, password) {
  try {
    console.log(`🔐 Authenticating user: ${email}`);
    
    // First, get the CSRF token
    const csrfResponse = await fetch(`${BASE_URL}/api/auth/csrf`);
    const csrfData = await csrfResponse.json();
    const csrfToken = csrfData.csrfToken;
    
    // Authenticate
    const authResponse = await fetch(`${BASE_URL}/api/auth/callback/credentials`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-CSRF-Token': csrfToken,
      },
      body: new URLSearchParams({
        email,
        password,
        csrfToken,
        callbackUrl: `${BASE_URL}/dashboard`,
        json: 'true'
      }),
      redirect: 'manual'
    });
    
    // Extract session cookie
    const cookies = authResponse.headers.get('set-cookie');
    if (!cookies) {
      throw new Error('No session cookie received');
    }
    
    const sessionCookie = cookies.split(';')[0];
    console.log(`✅ Authentication successful for ${email}`);
    
    return sessionCookie;
  } catch (error) {
    console.error(`❌ Authentication failed for ${email}:`, error.message);
    return null;
  }
}

async function testApiEndpoint(endpoint, sessionCookie, method = 'GET', body = null) {
  try {
    console.log(`🧪 Testing ${method} ${endpoint}`);
    
    const options = {
      method,
      headers: {
        'Cookie': sessionCookie,
        'Content-Type': 'application/json',
      }
    };
    
    if (body && method !== 'GET') {
      options.body = JSON.stringify(body);
    }
    
    const response = await fetch(`${BASE_URL}${endpoint}`, options);
    const data = await response.json();
    
    console.log(`   Status: ${response.status}`);
    
    if (response.ok) {
      console.log(`   ✅ Success`);
      if (data.data && Array.isArray(data.data)) {
        console.log(`   📊 Returned ${data.data.length} items`);
      } else if (data.data) {
        console.log(`   📊 Returned data object`);
      }
    } else {
      console.log(`   ❌ Error: ${data.error || 'Unknown error'}`);
    }
    
    return { status: response.status, data };
  } catch (error) {
    console.log(`   💥 Request failed: ${error.message}`);
    return { status: 0, error: error.message };
  }
}

async function runTests() {
  console.log('🚀 Starting RBAC API Tests');
  console.log('==========================\n');
  
  // Test with organization admin
  const testUser = TEST_USERS[0];
  const sessionCookie = await authenticateUser(testUser.email, testUser.password);
  
  if (!sessionCookie) {
    console.error('❌ Could not authenticate. Exiting.');
    process.exit(1);
  }
  
  console.log('\n📋 Testing API Endpoints:');
  console.log('-------------------------');
  
  // Test the problematic endpoint
  await testApiEndpoint('/api/rbac/users', sessionCookie);
  await testApiEndpoint('/api/rbac/users?limit=5', sessionCookie);
  
  // Test other endpoints
  await testApiEndpoint('/api/rbac/roles', sessionCookie);
  await testApiEndpoint('/api/rbac/permissions', sessionCookie);
  await testApiEndpoint('/api/rbac/user-permissions', sessionCookie);
  
  console.log('\n🎉 Tests completed!');
}

// Handle errors
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Run the tests
runTests().catch(error => {
  console.error('Test execution failed:', error);
  process.exit(1);
});
